"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  Search, 
  Filter,
  Play,
  Clock,
  Users,
  Eye,
  Calendar,
  Zap,
  Trophy,
  Target,
  RefreshCw
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "@/lib/toast-utils"
import { useRouter } from "next/navigation"
import { MinimalLayout } from "@/components/layouts/minimal-layout"
import { LiveQuizCard } from "@/components/student/live-quiz/live-quiz-card"
import { formatDistanceToNow } from "date-fns"

interface LiveQuizSession {
  id: string
  title: string
  description?: string
  status: 'WAITING' | 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'CANCELLED'
  maxParticipants?: number
  currentQuestion: number
  questionTimeLimit?: number
  autoAdvance: boolean
  showLeaderboard: boolean
  allowLateJoin: boolean
  startTime?: string
  scheduledStart?: string
  createdAt: string
  quiz: {
    id: string
    title: string
    description?: string
    difficulty: string
    timeLimit?: number
    questionCount: number
    category?: string
    tags: string[]
    thumbnail?: string
  }
  creator: {
    id: string
    name: string
  }
  participantCount: number
  isParticipating: boolean
  canJoin: boolean
  participants: Array<{
    id: string
    userName: string
  }>
}

export default function StudentLiveQuiz() {
  const router = useRouter()
  const [sessions, setSessions] = useState<LiveQuizSession[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("joinable")
  const [difficultyFilter, setDifficultyFilter] = useState<string>("all")
  const [refreshing, setRefreshing] = useState(false)

  // Pagination
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const itemsPerPage = 12

  useEffect(() => {
    fetchSessions()
    // Set up auto-refresh for live sessions
    const interval = setInterval(() => {
      if (!refreshing) {
        fetchSessions(true) // Silent refresh
      }
    }, 15000) // Refresh every 15 seconds

    return () => clearInterval(interval)
  }, [currentPage, statusFilter, difficultyFilter, searchTerm])

  const fetchSessions = async (silent = false) => {
    if (!silent) setLoading(true)
    setRefreshing(true)

    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: itemsPerPage.toString(),
        ...(statusFilter !== 'all' && statusFilter !== 'joinable' && { status: statusFilter }),
        ...(statusFilter === 'joinable' && { joinable: 'true' }),
        ...(difficultyFilter !== 'all' && { difficulty: difficultyFilter }),
        ...(searchTerm && { search: searchTerm })
      })

      const response = await fetch(`/api/student/live-quiz/available?${params}`)
      const data = await response.json()

      if (data.success) {
        setSessions(data.data.sessions)
        setTotalPages(data.data.pagination.totalPages)
      } else {
        throw new Error(data.message || 'Failed to fetch live quiz sessions')
      }
    } catch (error) {
      console.error('Error fetching live quiz sessions:', error)
      if (!silent) {
        toast.error('Failed to fetch live quiz sessions')
      }
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const handleJoinSession = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/student/live-quiz/sessions/${sessionId}/join`, {
        method: 'POST'
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Successfully joined live quiz!')
        router.push(`/student/live-quiz/${sessionId}`)
      } else {
        throw new Error(data.message || 'Failed to join session')
      }
    } catch (error) {
      console.error('Error joining session:', error)
      toast.error('Failed to join session')
    }
  }

  const getStatusStats = () => {
    const waiting = sessions.filter(s => s.status === 'WAITING').length
    const active = sessions.filter(s => s.status === 'ACTIVE').length
    const participating = sessions.filter(s => s.isParticipating).length
    
    return { waiting, active, participating }
  }

  const stats = getStatusStats()

  return (
    <MinimalLayout title="Live Quiz Sessions" userRole="STUDENT">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
              Live Quiz Sessions
            </h1>
            <p className="text-muted-foreground">Join live quiz sessions and compete in real-time</p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => fetchSessions()}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card className="glass">
              <CardContent className="pt-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                    <Clock className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold">{stats.waiting}</div>
                    <p className="text-sm text-muted-foreground">Waiting to Start</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="glass">
              <CardContent className="pt-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <Zap className="h-5 w-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold">{stats.active}</div>
                    <p className="text-sm text-muted-foreground">Active Now</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="glass">
              <CardContent className="pt-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <Trophy className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold">{stats.participating}</div>
                    <p className="text-sm text-muted-foreground">Your Sessions</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Filters */}
        <Card className="glass">
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search live quiz sessions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="joinable">Joinable</SelectItem>
                  <SelectItem value="all">All Sessions</SelectItem>
                  <SelectItem value="WAITING">Waiting</SelectItem>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                </SelectContent>
              </Select>
              <Select value={difficultyFilter} onValueChange={setDifficultyFilter}>
                <SelectTrigger className="w-[140px]">
                  <Target className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Difficulty" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Levels</SelectItem>
                  <SelectItem value="EASY">Easy</SelectItem>
                  <SelectItem value="MEDIUM">Medium</SelectItem>
                  <SelectItem value="HARD">Hard</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Sessions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <AnimatePresence>
            {loading ? (
              // Loading skeletons
              Array.from({ length: 6 }).map((_, i) => (
                <motion.div
                  key={`skeleton-${i}`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: i * 0.1 }}
                >
                  <Card className="h-80 animate-pulse glass">
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        <div className="h-4 bg-muted rounded w-3/4"></div>
                        <div className="h-3 bg-muted rounded w-1/2"></div>
                        <div className="h-3 bg-muted rounded w-full"></div>
                        <div className="h-3 bg-muted rounded w-2/3"></div>
                        <div className="h-8 bg-muted rounded w-full"></div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))
            ) : sessions.length > 0 ? (
              sessions.map((session, index) => (
                <motion.div
                  key={session.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <LiveQuizCard
                    session={session}
                    onJoin={handleJoinSession}
                    onView={(sessionId) => router.push(`/student/live-quiz/${sessionId}`)}
                  />
                </motion.div>
              ))
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="col-span-full"
              >
                <Card className="glass">
                  <CardContent className="pt-6 text-center">
                    <div className="text-muted-foreground">
                      <Zap className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No live quiz sessions found</p>
                      <p className="text-sm">Check back later for new sessions</p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center gap-2">
            <Button
              variant="outline"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <span className="flex items-center px-4 text-sm text-muted-foreground">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        )}
      </div>
    </MinimalLayout>
  )
}
